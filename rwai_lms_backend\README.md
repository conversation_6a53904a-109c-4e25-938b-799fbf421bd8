# 🤖 智能聊天系统

基于LlamaIndex的交互式聊天系统，支持RAG检索和纯对话两种模式。

## ✨ 功能特点

1. **🔄 持久化对话历史** - 使用Redis存储对话记录，支持跨会话恢复
2. **🎯 动态过滤检索** - 支持基于course_id或course_material_id的精确检索
3. **🤖 双引擎模式** - condense_plus_context（RAG检索）和simple（纯对话）
4. **📊 向量数据库** - 使用本机Qdrant向量数据库（端口6334）
5. **🎨 丰富提示词** - 费曼风格讲解，Markdown格式输出，emoji增强

## 🛠️ 环境要求

### 必需服务
- **Redis** - 端口6379，用于存储对话历史
- **Qdrant** - 端口6334，用于向量检索
- **OpenAI API** - 用于LLM和嵌入模型

### Python依赖
```bash
pip install llama-index
pip install llama-index-vector-stores-qdrant
pip install llama-index-llms-openai
pip install llama-index-embeddings-openai
pip install llama-index-storage-chat-store-redis
pip install qdrant-client
pip install redis
```

## ⚙️ 配置

### 1. 修改配置文件
编辑 `config.py` 文件，根据您的环境修改以下配置：

```python
# OpenAI API配置
OPENAI_CONFIG = {
    "api_key": "your-openai-api-key",  # 替换为您的API Key
    "base_url": "https://api.openai.com/v1",  # 根据需要修改
    # ... 其他配置
}

# Qdrant配置
QDRANT_CONFIG = {
    "collection_name": "your_collection_name"  # 替换为您的集合名称
}
```

### 2. 启动必需服务

#### 启动Redis
```bash
# 使用Docker
docker run -d -p 6379:6379 redis:latest

# 或使用本地安装
redis-server
```

#### 启动Qdrant
```bash
# 使用Docker
docker run -d -p 6333:6333 -p 6334:6334 qdrant/qdrant:latest

# 确保您的向量数据已经导入到指定的collection中
```

## 🚀 使用方法

### 启动程序
```bash
python interactive_chat_system.py
```

### 输入格式
程序会依次提示您输入以下信息：

1. **conversation_id** - 对话标识符，用于恢复历史对话
2. **course_id** - 课程ID（可选，与course_material_id互斥）
3. **course_material_id** - 课程材料ID（可选，与course_id互斥）
4. **chat_engine_type** - 引擎类型：
   - `condense_plus_context`: RAG检索模式
   - `simple`: 纯对话模式
5. **question** - 您的问题

### 使用示例

#### 示例1：RAG检索模式
```
conversation_id: user123_python_session
course_id: python_basics
course_material_id: (留空)
chat_engine_type: condense_plus_context
question: Python函数如何定义？
```

#### 示例2：纯对话模式
```
conversation_id: user456_chat_session
course_id: (留空)
course_material_id: material_001
chat_engine_type: simple
question: 你好，我想学习编程
```

## 🎯 核心功能说明

### 1. 对话记忆管理
- 使用Redis持久化存储对话历史
- 支持对话摘要，避免token超限
- 可通过conversation_id恢复历史对话

### 2. 动态过滤检索
- **course_id模式**：只检索指定课程的内容
- **course_material_id模式**：只检索指定材料的内容
- **无过滤模式**：检索全部内容

### 3. 双引擎架构
- **condense_plus_context**：
  - 先压缩历史对话和当前问题
  - 然后进行向量检索
  - 最后基于检索内容生成回答
- **simple**：
  - 直接基于对话历史生成回答
  - 不进行向量检索

## 🔧 自定义配置

### 提示词自定义
在 `config.py` 中的 `PROMPTS` 部分可以自定义：
- `condense_prompt`: 问题压缩提示词
- `context_prompt`: 上下文整合提示词
- `system_prompt`: 系统提示词

### 模型配置
在 `OPENAI_CONFIG` 中可以调整：
- 模型类型（gpt-4o-mini, gpt-4等）
- 温度参数
- 嵌入模型

## 🚪 退出程序
在任何输入提示时输入 `quit` 或 `exit` 即可退出程序。

## 📝 注意事项

1. **数据准备**：确保Qdrant中已有向量数据，且metadata包含course_id或course_material_id字段
2. **API配置**：确保OpenAI API Key有效且有足够额度
3. **服务状态**：确保Redis和Qdrant服务正常运行
4. **过滤互斥**：course_id和course_material_id不能同时使用

## 🐛 故障排除

### 常见问题
1. **连接失败**：检查Redis和Qdrant服务是否启动
2. **API错误**：检查OpenAI API Key和网络连接
3. **检索无结果**：检查collection_name和过滤条件是否正确
4. **内存不足**：调整MEMORY_CONFIG中的token_limit

### 日志查看
程序运行时会输出详细的状态信息，包括：
- 服务连接状态
- 过滤器应用情况
- 引擎创建状态
- 对话配置信息

## 📄 许可证
MIT License
