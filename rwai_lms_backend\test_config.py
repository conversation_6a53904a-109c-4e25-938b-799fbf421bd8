#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 配置测试脚本
用于验证系统配置是否正确
"""

import sys
import traceback

def test_imports():
    """测试导入"""
    print("🔍 测试Python包导入...")
    try:
        from qdrant_client import QdrantClient
        print("✅ qdrant_client 导入成功")
        
        from llama_index.vector_stores.qdrant import QdrantVectorStore
        print("✅ llama_index.vector_stores.qdrant 导入成功")
        
        from llama_index.core import StorageContext, VectorStoreIndex, Settings, PromptTemplate
        print("✅ llama_index.core 导入成功")
        
        from llama_index.llms.openai import OpenAI
        print("✅ llama_index.llms.openai 导入成功")
        
        from llama_index.embeddings.openai import OpenAIEmbedding
        print("✅ llama_index.embeddings.openai 导入成功")
        
        from llama_index.storage.chat_store.redis import RedisChatStore
        print("✅ llama_index.storage.chat_store.redis 导入成功")
        
        from llama_index.core.memory import ChatSummaryMemoryBuffer
        print("✅ llama_index.core.memory 导入成功")
        
        from llama_index.core.chat_engine import SimpleChatEngine
        print("✅ llama_index.core.chat_engine 导入成功")
        
        from llama_index.core.vector_stores import MetadataFilter, MetadataFilters, FilterOperator
        print("✅ llama_index.core.vector_stores 导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\n📋 测试配置文件...")
    try:
        from rwai_lms_backend.config import OPENAI_CONFIG, QDRANT_CONFIG, REDIS_CONFIG, MEMORY_CONFIG, PROMPTS, UI_CONFIG
        print("✅ 配置文件导入成功")
        
        # 检查必要的配置项
        required_openai_keys = ["api_key", "base_url", "model", "temperature", "embedding_model"]
        for key in required_openai_keys:
            if key not in OPENAI_CONFIG:
                print(f"❌ OPENAI_CONFIG 缺少 {key}")
                return False
        print("✅ OPENAI_CONFIG 配置完整")
        
        required_qdrant_keys = ["host", "port", "prefer_grpc", "timeout", "collection_name"]
        for key in required_qdrant_keys:
            if key not in QDRANT_CONFIG:
                print(f"❌ QDRANT_CONFIG 缺少 {key}")
                return False
        print("✅ QDRANT_CONFIG 配置完整")
        
        required_redis_keys = ["redis_url", "ttl"]
        for key in required_redis_keys:
            if key not in REDIS_CONFIG:
                print(f"❌ REDIS_CONFIG 缺少 {key}")
                return False
        print("✅ REDIS_CONFIG 配置完整")
        
        return True
    except ImportError as e:
        print(f"❌ 配置文件导入失败: {e}")
        return False

def test_redis_connection():
    """测试Redis连接"""
    print("\n💾 测试Redis连接...")
    try:
        import redis
        from rwai_lms_backend.config import REDIS_CONFIG
        
        # 解析Redis URL
        redis_url = REDIS_CONFIG["redis_url"]
        if redis_url.startswith("redis://"):
            host = redis_url.replace("redis://", "").split(":")[0]
            port = int(redis_url.replace("redis://", "").split(":")[1]) if ":" in redis_url.replace("redis://", "") else 6379
        else:
            host = "localhost"
            port = 6379
        
        client = redis.Redis(host=host, port=port, decode_responses=True)
        client.ping()
        print(f"✅ Redis连接成功 ({host}:{port})")
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        print("💡 请确保Redis服务正在运行")
        return False

def test_qdrant_connection():
    """测试Qdrant连接"""
    print("\n📊 测试Qdrant连接...")
    try:
        from qdrant_client import QdrantClient
        from rwai_lms_backend.config import QDRANT_CONFIG
        
        client = QdrantClient(
            host=QDRANT_CONFIG["host"],
            port=QDRANT_CONFIG["port"],
            prefer_grpc=QDRANT_CONFIG["prefer_grpc"],
            timeout=QDRANT_CONFIG["timeout"]
        )
        
        # 尝试获取集合信息
        collections = client.get_collections()
        print(f"✅ Qdrant连接成功 ({QDRANT_CONFIG['host']}:{QDRANT_CONFIG['port']})")
        
        # 检查指定的集合是否存在
        collection_name = QDRANT_CONFIG["collection_name"]
        collection_exists = any(col.name == collection_name for col in collections.collections)
        
        if collection_exists:
            print(f"✅ 集合 '{collection_name}' 存在")
            
            # 获取集合信息
            collection_info = client.get_collection(collection_name)
            print(f"📊 集合信息: {collection_info.vectors_count} 个向量")
        else:
            print(f"❌ 集合 '{collection_name}' 不存在")
            print("💡 请确保向量数据已导入到指定集合")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Qdrant连接失败: {e}")
        print("💡 请确保Qdrant服务正在运行")
        return False

def test_openai_config():
    """测试OpenAI配置"""
    print("\n🤖 测试OpenAI配置...")
    try:
        from rwai_lms_backend.config import OPENAI_CONFIG
        import os
        
        # 检查API Key
        api_key = OPENAI_CONFIG["api_key"]
        if not api_key or api_key == "your-openai-api-key":
            print("❌ 请在config.py中设置有效的OpenAI API Key")
            return False
        
        print("✅ API Key已配置")
        print(f"✅ 模型: {OPENAI_CONFIG['model']}")
        print(f"✅ 嵌入模型: {OPENAI_CONFIG['embedding_model']}")
        print(f"✅ 基础URL: {OPENAI_CONFIG['base_url']}")
        
        return True
    except Exception as e:
        print(f"❌ OpenAI配置检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始系统配置测试...\n")
    
    tests = [
        ("Python包导入", test_imports),
        ("配置文件", test_config),
        ("Redis连接", test_redis_connection),
        ("Qdrant连接", test_qdrant_connection),
        ("OpenAI配置", test_openai_config),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("="*50)
    if all_passed:
        print("🎉 所有测试通过！系统配置正确。")
        print("💡 您可以运行 python interactive_chat_system.py 启动聊天系统")
    else:
        print("⚠️  部分测试失败，请检查配置和服务状态")
        print("💡 请参考README.md进行故障排除")

if __name__ == "__main__":
    main()
