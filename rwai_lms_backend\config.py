#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚙️ 聊天系统配置文件
请根据您的实际环境修改以下配置
"""

# 🔑 OpenAI API配置
OPENAI_CONFIG = {
    "api_key": "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE",  # 请替换为您的API Key
    "base_url": "https://api.openai-proxy.org/v1",  # 请根据需要修改
    "model": "gpt-4o-mini",
    "temperature": 0.1,
    "embedding_model": "text-embedding-3-small"
}

# 🗄️ Qdrant向量数据库配置
QDRANT_CONFIG = {
    "host": "localhost",
    "port": 6334,  # gRPC端口
    "prefer_grpc": True,
    "timeout": 10,
    "collection_name": "course_materials"  # 请根据您的实际集合名称修改
}

# 💾 Redis配置
REDIS_CONFIG = {
    "redis_url": "redis://localhost:6379",
    "ttl": 3600  # 对话存活时间（秒）
}

# 🧠 Memory配置
MEMORY_CONFIG = {
    "token_limit": 4000,  # 内存token限制
    "summary_prompt": """你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。
"""
}

# 🎨 提示词配置
PROMPTS = {
    # condense_question用的提示词
    "condense_prompt": """你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。
注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。
=== 聊天历史 ===
{chat_history}

=== 学生最新提出的问题 ===
{question}

=== 改写后的独立问题 ===
""",
    
    # context_prompt（整合检索内容和用户问题的核心提示词）
    "context_prompt": """你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。

📚 **相关文档内容：**
{context_str}

🎯 **回答要求：**
1. 严格基于上述文档内容进行回答
2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'
3. 回答要条理清晰，使用适当的emoji让内容更生动
4. 请引用具体的文档内容来支撑你的回答

💡 **请基于以上文档和之前的对话历史来回答用户的问题。**根据以上信息，请回答这个问题: {query_str}

====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================

""",
    
    # simple引擎的系统提示词
    "system_prompt": """你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。"""
}

# 🎮 界面配置
UI_CONFIG = {
    "welcome_message": """
🎯 欢迎使用智能聊天系统！
📋 每次提问需要输入以下信息：
   - conversation_id: 对话ID（用于标识和恢复对话）
   - course_id: 课程ID（可选，与course_material_id互斥）
   - course_material_id: 课程材料ID（可选，与course_id互斥）
   - chat_engine_type: 聊天引擎类型
     * condense_plus_context: RAG检索模式，会搜索相关文档
     * simple: 纯对话模式，不进行文档检索
   - question: 您的问题
💡 提示：输入 'quit' 或 'exit' 退出程序
""",
    
    "usage_examples": [
        {
            "title": "示例1 - 使用课程ID进行RAG检索：",
            "conversation_id": "user123_session1",
            "course_id": "python_basics",
            "course_material_id": "",
            "chat_engine_type": "condense_plus_context",
            "question": "Python函数如何定义？"
        },
        {
            "title": "示例2 - 使用材料ID进行简单对话：",
            "conversation_id": "user456_session2",
            "course_id": "",
            "course_material_id": "material_001",
            "chat_engine_type": "simple",
            "question": "你好，我想学习编程"
        }
    ]
}
