#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 基于LlamaIndex的交互式聊天系统
功能：
1. 支持用户输入conversation_id, course_id, course_material_id, chat_engine_type和问题
2. 使用Redis作为聊天存储，支持持久化对话历史
3. 支持两种聊天引擎：condense_plus_context（RAG检索）和simple（纯对话）
4. 支持基于course_id或course_material_id的动态过滤检索
5. 使用本机Qdrant向量数据库（端口6334）
"""

import os
from qdrant_client import QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex, Settings, PromptTemplate
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.storage.chat_store.redis import RedisChatStore
from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.core.chat_engine import SimpleChatEngine
from llama_index.core.vector_stores import MetadataFilter, MetadataFilters, FilterOperator

# 导入配置文件
from rwai_lms_backend.config import OPENAI_CONFIG, QDRANT_CONFIG, REDIS_CONFIG, MEMORY_CONFIG, PROMPTS, UI_CONFIG

# 🔧 全局配置
print("🚀 正在初始化聊天系统...")

# OpenAI API配置（从配置文件读取）
os.environ["OPENAI_API_KEY"] = OPENAI_CONFIG["api_key"]
os.environ["OPENAI_BASE_URL"] = OPENAI_CONFIG["base_url"]

# 设置全局LLM和嵌入模型
Settings.llm = OpenAI(
    model=OPENAI_CONFIG["model"],
    temperature=OPENAI_CONFIG["temperature"],
    api_base=OPENAI_CONFIG["base_url"]
)
Settings.embed_model = OpenAIEmbedding(
    model=OPENAI_CONFIG["embedding_model"],
    api_base=OPENAI_CONFIG["base_url"]
)

# 🗄️ 连接Qdrant向量数据库
print("📊 连接Qdrant向量数据库...")
qdrant_client = QdrantClient(
    host=QDRANT_CONFIG["host"],
    port=QDRANT_CONFIG["port"],
    prefer_grpc=QDRANT_CONFIG["prefer_grpc"],
    timeout=QDRANT_CONFIG["timeout"]
)

# 从已有集合创建向量存储
collection_name = QDRANT_CONFIG["collection_name"]
vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)
storage_context = StorageContext.from_defaults(vector_store=vector_store)

# 从Qdrant向量存储创建index
index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)
print(f"✅ 已从Qdrant {QDRANT_CONFIG['port']}端口加载向量数据到index")

# 🧠 创建Redis聊天存储
print("💾 连接Redis聊天存储...")
chat_store = RedisChatStore(
    redis_url=REDIS_CONFIG["redis_url"],
    ttl=REDIS_CONFIG["ttl"]
)

# 📝 自定义提示词模板（从配置文件读取）
new_condense_prompt = PromptTemplate(PROMPTS["condense_prompt"])
custom_context_prompt = PROMPTS["context_prompt"]
custom_summary_prompt = MEMORY_CONFIG["summary_prompt"]

def create_memory_and_engines(conversation_id, course_id=None, course_material_id=None):
    """
    🏗️ 根据用户输入创建memory和chat engines
    
    Args:
        conversation_id: 对话ID，用作Redis中的chat_store_key
        course_id: 课程ID，用于过滤检索（与course_material_id互斥）
        course_material_id: 课程材料ID，用于过滤检索（与course_id互斥）
    
    Returns:
        tuple: (memory, condense_question_plus_engine, simple_engine)
    """
    print(f"🔧 创建对话记忆，conversation_id: {conversation_id}")
    
    # 创建共享的ChatSummaryMemoryBuffer，使用用户输入的conversation_id作为key
    memory = ChatSummaryMemoryBuffer.from_defaults(
        token_limit=MEMORY_CONFIG["token_limit"],
        llm=Settings.llm,
        chat_store=chat_store,
        chat_store_key=conversation_id,  # 🔑 使用用户输入的conversation_id
        summarize_prompt=custom_summary_prompt
    )
    
    # 🎯 创建带过滤功能的检索器
    filters_list = []
    
    # 根据用户输入设置过滤条件（course_id和course_material_id只能存在一个）
    if course_id:
        print(f"📋 设置course_id过滤: {course_id}")
        filters_list.append(
            MetadataFilter(key="course_id", value=course_id, operator=FilterOperator.EQ)
        )
    elif course_material_id:
        print(f"📄 设置course_material_id过滤: {course_material_id}")
        filters_list.append(
            MetadataFilter(key="course_material_id", value=course_material_id, operator=FilterOperator.EQ)
        )
    
    # 创建过滤器
    if filters_list:
        filters = MetadataFilters(filters=filters_list)
        print(f"🔍 应用过滤器: {filters}")
    else:
        filters = None
        print("🌐 无过滤器，检索全部内容")
    
    # 🤖 创建condense_plus_context引擎（RAG检索模式）
    condense_question_plus_engine = index.as_chat_engine(
        chat_mode="condense_plus_context",
        condense_prompt=new_condense_prompt,
        context_prompt=custom_context_prompt,
        memory=memory,
        verbose=True,
        filters=filters  # 应用过滤器
    )
    
    # 🗨️ 创建simple引擎（纯对话模式）
    simple_engine = SimpleChatEngine.from_defaults(
        llm=Settings.llm,
        memory=memory,
        system_prompt=PROMPTS["system_prompt"],
        verbose=True
    )
    
    print("✅ Memory和聊天引擎创建完成")
    return memory, condense_question_plus_engine, simple_engine

def main():
    """
    🎮 主交互循环
    """
    print("\n" + "="*80)
    print(UI_CONFIG["welcome_message"])
    print("="*80 + "\n")
    
    while True:
        try:
            # 📝 获取用户输入
            print("🔤 请输入对话信息：")
            conversation_id = input("conversation_id: ").strip()

            # 检查退出命令
            if conversation_id.lower() in ['quit', 'exit']:
                print("👋 再见！")
                break

            if not conversation_id:
                print("❌ conversation_id不能为空！")
                continue
            
            course_id = input("course_id (可选，按回车跳过): ").strip() or None
            course_material_id = input("course_material_id (可选，按回车跳过): ").strip() or None
            
            # 检查course_id和course_material_id不能同时存在
            if course_id and course_material_id:
                print("❌ course_id和course_material_id不能同时存在，请只选择其中一个！")
                continue
            
            chat_engine_type = input("chat_engine_type (condense_plus_context/simple): ").strip()
            if chat_engine_type not in ["condense_plus_context", "simple"]:
                print("❌ chat_engine_type必须是 'condense_plus_context' 或 'simple'！")
                continue
            
            question = input("question: ").strip()
            if not question:
                print("❌ 问题不能为空！")
                continue

            # 检查问题中的退出命令
            if question.lower() in ['quit', 'exit']:
                print("👋 再见！")
                break
            
            # 📊 显示当前配置
            print(f"\n📋 当前配置：")
            print(f"   🆔 对话ID: {conversation_id}")
            print(f"   📚 课程ID: {course_id if course_id else '无'}")
            print(f"   📄 材料ID: {course_material_id if course_material_id else '无'}")
            print(f"   🤖 引擎类型: {chat_engine_type}")
            print(f"   ❓ 问题: {question}")

            # 🏗️ 创建memory和engines
            memory, condense_question_plus_engine, simple_engine = create_memory_and_engines(
                conversation_id, course_id, course_material_id
            )
            
            # 🎯 根据用户选择的引擎类型进行对话
            print(f"\n🤖 使用 {chat_engine_type} 引擎回答...")
            
            if chat_engine_type == "condense_plus_context":
                current_engine = condense_question_plus_engine
            else:  # simple
                current_engine = simple_engine
            
            # 💬 获取回答
            response = current_engine.chat(question)
            print(f"\n🎉 回答：\n{response}\n")
            print("-" * 60)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 出错了: {e}")
            continue

def show_usage_example():
    """
    📖 显示使用示例
    """
    print("\n" + "="*60)
    print("📖 使用示例：")
    print("="*60)

    for example in UI_CONFIG["usage_examples"]:
        print(example["title"])
        print(f"  conversation_id: {example['conversation_id']}")
        print(f"  course_id: {example['course_id'] if example['course_id'] else '(留空)'}")
        print(f"  course_material_id: {example['course_material_id'] if example['course_material_id'] else '(留空)'}")
        print(f"  chat_engine_type: {example['chat_engine_type']}")
        print(f"  question: {example['question']}")
        print()

    print("="*60 + "\n")

if __name__ == "__main__":
    # 显示使用示例
    show_usage_example()

    # 启动主程序
    main()
